// src/app/services/help-guide.service.ts
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { HelpGuide, HelpCategory, HelpContent, HelpDialogData } from '../models/help-guide.model';
import { HelpDialogComponent } from '../components/help-dialog/help-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class HelpGuideService {
  private guides: Map<string, HelpGuide> = new Map();

  constructor(private dialog: MatDialog) {
    this.initializeGuides();
  }

  private initializeGuides(): void {
    // Müşteri Yönetimi Rehberleri
    this.addGuide({
      id: 'allmembers',
      title: 'Bütün Üyeler',
      description: 'Salonunuza kayıtlı tüm üyelerinizi görün ve yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-users',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada salonunuza kayıtlı olan tüm üyelerinizi görebilirsiniz. İstediğiniz üyeyi kolayca bulabilir, bilgilerini düzenleyebilir veya gerektiğinde silebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Tüm üyelerinizi liste halinde görün',
            'Üye adı veya telefon numarası ile arama yapın',
            'Erkek veya kadın üyeleri ayrı ayrı filtreleyin',
            'Liste veya kart şeklinde görüntüleyin',
            'Üye bilgilerini düzenleyin',
            'Artık gelmeyen üyeleri silin',
            'Üye fotoğraflarını görün'
          ]
        },
        {
          type: 'steps',
          content: [
            'Aradığınız üyenin adını veya telefon numarasını yazın',
            'Sadece erkek veya kadın üyeleri görmek istiyorsanız filtreyi kullanın',
            'Liste çok uzunsa, sayfa başına gösterilecek üye sayısını artırın',
            'Üye bilgilerini değiştirmek için "Düzenle" butonuna tıklayın',
            'Üyeyi silmek için "Sil" butonuna tıklayın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'memberfilter',
      title: 'Aktif Üyeler',
      description: 'Şu anda üyeliği devam eden üyelerinizi görün',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-check',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyeliği hala devam eden üyelerinizi görebilirsiniz. Hangi üyenin kaç günü kaldığını görebilir, gerektiğinde üyeliklerini dondurabilir veya uzatabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üyeliği devam eden tüm üyelerinizi görün',
            'Üye adı veya telefonu ile arama yapın',
            'Sadece erkek veya kadın üyeleri listeleyin',
            'Branşa göre üyeleri ayırın (Fitness, Pilates vs.)',
            'Her üyenin kaç günü kaldığını görün',
            'Üyeliği geçici olarak dondurun',
            'Üyelik süresini uzatın'
          ]
        },
        {
          type: 'steps',
          content: [
            'Aradığınız üyenin adını veya telefonunu yazın',
            'Sadece belirli cinsiyetteki üyeleri görmek için filtreyi kullanın',
            'Branş filtresini kullanarak spor türüne göre ayırın',
            'Kalan gün sayısına dikkat edin (kırmızı olanlar yakında bitiyor)',
            'Üye işlemleri için sağdaki butonları kullanın'
          ]
        }
      ]
    });

    // E-Money Rehberleri
    this.addGuide({
      id: 'memberbalancetopup',
      title: 'Bakiye Yükle - Düşür',
      description: 'Üyelerinizin dijital bakiyelerini yönetin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-money-bill-wave',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyelerinizin dijital cüzdanlarını yönetebilirsiniz. Üyelerinize bakiye yükleyebilir, hatalı yüklemeleri düzeltebilir ve bakiye durumlarını takip edebilirsiniz. Üyeler bu bakiye ile ürün satın alabilir.'
        },
        {
          type: 'list',
          content: [
            'Bakiyesi olan tüm üyeleri görün',
            'Üye adı veya telefonu ile arama yapın',
            'Pozitif veya negatif bakiyeleri filtreleyin',
            'Toplam bakiye istatistiklerini görün',
            'Üyelere bakiye yükleyin',
            'Hatalı işlemleri düzeltin',
            'Her işlem için açıklama ekleyin'
          ]
        },
        {
          type: 'steps',
          content: [
            'Bakiye işlemi yapmak istediğiniz üyeyi bulun',
            'Arama kutusuna üyenin adını veya telefon numarasını yazın',
            'Listeden doğru üyeyi seçin',
            'Yüklemek için pozitif, düşürmek için negatif tutar girin',
            'İşlemin sebebini açıklama kısmına yazın',
            'İşlemi onaylayın'
          ]
        }
      ]
    });

    // Antrenman Rehberleri
    this.addGuide({
      id: 'exercises',
      title: 'Egzersiz Listesi',
      description: 'Spor salonunuz için egzersiz veritabanını yönetin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-list-ul',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada antrenman programlarında kullanacağınız egzersizleri görebilir ve yönetebilirsiniz. Sistemde hazır egzersizler var, ayrıca kendi özel egzersizlerinizi de ekleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Sistemde hazır olan egzersizleri görün',
            'Kendi eklediğiniz özel egzersizleri görün',
            'Tüm egzersizleri birlikte görüntüleyin',
            'Vücut bölgesine göre filtreleyin (Göğüs, Sırt, Kol vs.)',
            'Zorluk seviyesine göre ayırın',
            'Hangi ekipmanla yapıldığına göre filtreleyin',
            'Egzersiz adı ile arama yapın',
            'Yeni egzersiz ekleyin',
            'Kendi egzersizlerinizi düzenleyin'
          ]
        },
        {
          type: 'steps',
          content: [
            'Hangi egzersizleri görmek istediğinizi seçin (Sistem/Salon/Hepsi)',
            'Vücut bölgesi filtresini kullanarak istediğiniz kas grubunu seçin',
            'Zorluk seviyesini belirleyin (Kolay, Orta, Zor)',
            'Hangi ekipmanla yapılan egzersizleri görmek istiyorsanız filtreleyin',
            'Belirli bir egzersizi bulmak için arama kutusunu kullanın',
            'Yeni egzersiz eklemek için "+" butonuna tıklayın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'frozen-memberships',
      title: 'Dondurulmuş Üyelikler',
      description: 'Geçici olarak üyeliği durdurulan üyelerinizi görün',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-snowflake',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Bazen üyeleriniz hastalık, seyahat veya başka nedenlerle geçici olarak salona gelemeyebilir. Bu durumda üyeliklerini "dondurarak" süreyi durdurabilirsiniz. Bu sayfada dondurulmuş üyelikleri görebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Şu anda dondurulmuş olan üyelikleri görün',
            'Hangi tarihte dondurulduğunu öğrenin',
            'Dondurma süresinin ne kadar olduğunu görün',
            'Üyenin iletişim bilgilerine ulaşın',
            'Dondurma sebebini okuyun',
            'Geçmiş dondurma işlemlerini inceleyin'
          ]
        },
        {
          type: 'steps',
          content: [
            'Dondurulmuş üyelerin listesini inceleyin',
            'Hangi üyenin ne zaman dondurulduğunu kontrol edin',
            'Üye geri geldiğinde dondurma işlemini bitirin',
            'Gerekirse dondurma süresini uzatın',
            'Üyeyle iletişim kurmak için telefon numarasını kullanın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'memberremainingday',
      title: 'Üyelik Bitişi Yaklaşanlar',
      description: 'Üyeliği yakında bitecek olan üyelerinizi görün',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-hourglass-end',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyeliği 7 gün veya daha az sürede bitecek olan üyelerinizi görebilirsiniz. Bu üyelerle zamanında iletişime geçerek üyeliklerini yenileyebilir ve üye kaybını önleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üyeliği 7 gün içinde bitecek üyeleri görün',
            'Her üyenin tam olarak kaç günü kaldığını öğrenin',
            'Üyenin telefon numarasına hızlıca ulaşın',
            'Son ödeme tarihini ve tutarını kontrol edin',
            'Hangi üyelik paketini kullandığını görün',
            'Üye adı ile arama yapın'
          ]
        },
        {
          type: 'steps',
          content: [
            'Listeyi kontrol ederek hangi üyelerin üyeliği bitiyor görün',
            'Önce en az günü kalan üyelerle iletişime geçin',
            'Üyeyi arayarak üyelik yenileme teklifi yapın',
            'Ödeme aldıktan sonra üyeliği uzatın',
            'Düzenli olarak bu listeyi kontrol edin'
          ]
        }
      ]
    });

    // E-Money Rehberleri devam
    this.addGuide({
      id: 'products',
      title: 'Ürün Ekle',
      description: 'Salonunuzda satacağınız ürünleri ekleyin ve yönetin',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-shopping-basket',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada salonunuzda satacağınız ürünleri ekleyebilir ve yönetebilirsiniz. Protein tozu, spor içeceği, havlu gibi ürünleri sisteme ekleyerek üyelerinize satabilirsiniz. Sol tarafta yeni ürün ekleme, sağ tarafta mevcut ürünler bulunur.'
        },
        {
          type: 'list',
          content: [
            'Yeni ürün ekleyin (protein, içecek, aksesuar vs.)',
            'Ürün adını ve fiyatını belirleyin',
            'Mevcut ürünlerin listesini görün',
            'Ürün adı ile arama yapın',
            'Ürün bilgilerini düzenleyin',
            'Artık satmadığınız ürünleri silin',
            'Fiyata göre sıralama yapın'
          ]
        },
        {
          type: 'steps',
          content: [
            'Sol taraftaki forma ürün adını yazın (örn: Protein Tozu)',
            'Ürünün satış fiyatını girin',
            '"Ürün Ekle" butonuna tıklayın',
            'Sağ tarafta eklenen ürünü kontrol edin',
            'Fiyat değiştirmek için "Düzenle" butonunu kullanın',
            'Ürün aramak için arama kutusunu kullanın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'product-sale',
      title: 'Ürün Sat',
      description: 'Üyelerinize ürün satışı yapın ve ödeme alın',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-shopping-cart',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyelerinize ürün satışı yapabilirsiniz. Önce üyeyi seçin, sonra satacağınız ürünleri sepete ekleyin. Ödemeyi nakit olarak alabilir veya üyenin bakiyesinden düşebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üye seçin (ad veya telefon ile arama)',
            'Ürün seçin (ürün adı yazarak bulun)',
            'Kaç adet satacağınızı belirleyin',
            'Ürünleri sepete ekleyin',
            'Sepetten ürün çıkarabilirsiniz',
            'Toplam tutarı görün',
            'Nakit veya bakiye ile ödeme alın'
          ]
        },
        {
          type: 'steps',
          content: [
            'Üye arama kutusuna üyenin adını veya telefon numarasını yazın',
            'Açılan listeden doğru üyeyi seçin',
            'Ürün arama kutusuna ürün adını yazın',
            'Ürünü seçin ve kaç adet satacağınızı girin',
            '"Sepete Ekle" butonuna tıklayın',
            'Sepeti kontrol edin, gerekirse ürün çıkarın',
            'Ödeme yöntemini seçin ve satışı tamamlayın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'transactions',
      title: 'İşlem Takibi',
      description: 'Tüm finansal işlemleri takip edin ve raporlayın',
      category: HelpCategory.E_MONEY,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-exchange-alt',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada yaptığınız tüm ürün satışlarını görebilirsiniz. Hangi üyeye ne sattığınızı, ödemesini alıp almadığınızı takip edebilirsiniz. Günlük ve aylık satış raporlarınızı da buradan alabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Tüm ürün satış işlemlerini görün',
            'Ödenen ve ödenmemiş satışları ayırın',
            'Hangi üyeye ne sattığınızı öğrenin',
            'Belirli tarih aralığındaki satışları filtreleyin',
            'Toplam satış tutarlarını görün',
            'Ödeme durumlarını güncelleyin',
            'Günlük ve aylık satış raporları alın'
          ]
        },
        {
          type: 'steps',
          content: [
            'İşlem listesini inceleyin',
            'Belirli bir dönemi görmek için tarih aralığı seçin',
            'Üye adı ile arama yaparak o üyenin alışverişlerini görün',
            'Ödenmemiş işlemleri kontrol edin',
            'Ödeme alındığında durumu güncelleyin',
            'Satış performansınızı analiz edin'
          ]
        }
      ]
    });

    // Antrenman Rehberleri devam
    this.addGuide({
      id: 'workout-programs',
      title: 'Antrenman Programları',
      description: 'Üyeleriniz için antrenman programları oluşturun ve yönetin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-dumbbell',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyeleriniz için antrenman programları oluşturabilir ve yönetebilirsiniz. Yeni başlayanlar için kolay, deneyimliler için zor programlar hazırlayabilirsiniz. Her program 7 günlük olarak düzenlenir.'
        },
        {
          type: 'list',
          content: [
            'Mevcut antrenman programlarını görün',
            'Program adı ile arama yapın',
            'Seviyeye göre filtreleyin (Başlangıç, Orta, İleri)',
            'Hedefe göre ayırın (Kilo Verme, Kas Kazanma vs.)',
            'Yeni program oluşturun',
            'Mevcut programları düzenleyin',
            'Program detaylarını görüntüleyin',
            'Haftalık program yapısı kurun'
          ]
        },
        {
          type: 'steps',
          content: [
            'Mevcut programların listesini inceleyin',
            'Belirli bir programı bulmak için arama kutusunu kullanın',
            'Seviye ve hedef filtrelerini kullanarak programları ayırın',
            'Yeni program oluşturmak için "+" butonuna tıklayın',
            'Program detaylarını görmek için "Detay" butonunu kullanın',
            'Programı değiştirmek için "Düzenle" butonunu kullanın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'member-workout-assignments',
      title: 'Program Atamaları',
      description: 'Üyelere antrenman programları atayın ve takip edin',
      category: HelpCategory.TRAINING,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-plus',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyelerinize antrenman programları atayabilir ve mevcut atamaları yönetebilirsiniz. Hangi üyenin hangi programı takip ettiğini görebilir, yeni atamalar yapabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Şu anda aktif olan program atamalarını görün',
            'Hangi üyenin hangi programı takip ettiğini öğrenin',
            'Program atama tarihlerini kontrol edin',
            'Üye veya program adı ile arama yapın',
            'Yeni üyelere program atayın',
            'Mevcut atamaları değiştirin veya silin',
            'Sayfa sayfa atamaları görüntüleyin'
          ]
        },
        {
          type: 'steps',
          content: [
            'Mevcut program atamalarının listesini inceleyin',
            'Belirli bir üye veya programı bulmak için arama yapın',
            'Yeni atama yapmak için "Program Ata" butonuna tıklayın',
            'Üyeyi ve atayacağınız programı seçin',
            'Programın başlayacağı tarihi belirleyin',
            'Gerektiğinde atamaları düzenleyin veya silin'
          ]
        }
      ]
    });

    // Owner/Salon Yönetimi Rehberleri
    this.addGuide({
      id: 'license-dashboard',
      title: 'Ana Panel',
      description: 'Sistem geneli istatistikleri ve önemli bilgileri görüntüleyin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-tachometer-alt',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sistem yöneticisi olarak tüm salonlarınızın genel durumunu, lisans bilgilerini ve önemli istatistikleri bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam salon sayısı',
            'Aktif lisans sayısı',
            'Yakında dolacak lisanslar',
            'Günlük/aylık gelir istatistikleri',
            'Sistem kullanım oranları'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'company/unified-add',
      title: 'Yeni Salon Ekle',
      description: 'Sisteme yeni spor salonu ekleyin ve lisans atayın',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-plus-square',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sisteme yeni bir spor salonu ekleyebilir, salon sahibini tanımlayabilir ve lisans paketini atayabilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Salon bilgilerini girin (ad, adres, telefon)',
            'Salon sahibinin bilgilerini ekleyin',
            'Lisans paketini seçin',
            'Lisans süresini belirleyin',
            'Ödeme bilgilerini girin',
            'Salonu sisteme kaydedin'
          ]
        }
      ]
    });

    // Sistem Yönetimi Rehberleri
    this.addGuide({
      id: 'roles',
      title: 'Rol Yönetimi',
      description: 'Sistem rollerini tanımlayın ve yetkileri yönetin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-user-shield',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Sistemde kullanılacak rolleri tanımlayabilir ve her role hangi yetkilerin verileceğini belirleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Owner: Sistem yöneticisi (tüm yetkiler)',
            'Admin: Salon yöneticisi (salon işlemleri)',
            'Member: Üye (sınırlı yetkiler)',
            'Özel roller tanımlayabilirsiniz'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'user-roles',
      title: 'Kullanıcı Rolleri',
      description: 'Kullanıcılara rol atayın ve yetkileri düzenleyin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-users-cog',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Sistem kullanıcılarına roller atayabilir ve mevcut rol atamalarını yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Kullanıcı listesini görüntüleyin',
            'Rol atamak istediğiniz kullanıcıyı seçin',
            'Uygun rolü seçin',
            'Atamayı kaydedin',
            'Değişikliklerin etkili olduğunu kontrol edin'
          ]
        }
      ]
    });

    // Genel Rehberler
    this.addGuide({
      id: 'todayentries',
      title: 'Giriş-Çıkış Kayıtları',
      description: 'Üyelerinizin salona giriş-çıkış kayıtlarını görün',
      category: HelpCategory.GENERAL,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-door-open',
      priority: 1,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyelerinizin salona ne zaman girip çıktığını görebilirsiniz. Hangi üyenin ne sıklıkla geldiğini takip edebilir, düzenli gelmeyen üyelerle iletişime geçebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üye adı veya telefonu ile arama yapın',
            'Belirli bir tarihteki giriş-çıkışları görün',
            'Giriş ve çıkış saatlerini kontrol edin',
            'Üyenin geçmiş tüm kayıtlarını inceleyin',
            'QR kod ile yapılan girişleri görün',
            'Günlük toplam ziyaretçi sayısını öğrenin'
          ]
        },
        {
          type: 'steps',
          content: [
            'Arama kutusuna üyenin adını veya telefon numarasını yazın',
            'Listeden istediğiniz üyeyi seçin',
            'Belirli bir günü görmek için tarih seçin',
            'Tüm geçmişi görmek için tarihi boş bırakın',
            'Giriş-çıkış saatlerini ve sıklığını inceleyin'
          ]
        }
      ]
    });

    // Müşteri Yönetimi - Eksik Paneller
    this.addGuide({
      id: 'debtormember',
      title: 'Borçlu Üyeler',
      description: 'Ödeme borcu olan üyelerinizi görün ve takip edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-hand-holding-usd',
      priority: 5,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyelik ödemelerini henüz tamamlamamış olan üyelerinizi görebilirsiniz. Hangi üyenin ne kadar borcu olduğunu görüp, onlarla iletişime geçerek ödemelerini alabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam kaç üyenizin borcu olduğunu görün',
            'Toplam borç tutarını öğrenin',
            'Her üyenin ne kadar borcu olduğunu görün',
            'Son ödeme tarihlerini kontrol edin',
            'Üyenin telefon numarasına ulaşın',
            'Ödeme aldığınızda hızlıca kaydedin'
          ]
        },
        {
          type: 'steps',
          content: [
            'Borçlu üyeler listesini inceleyin',
            'En çok borcu olan üyelerden başlayın',
            'Üyeyi arayarak ödeme hatırlatması yapın',
            'Ödeme planı konusunda anlaşın',
            'Aldığınız ödemeleri sisteme girin',
            'Düzenli olarak bu listeyi kontrol edin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'birthdays',
      title: 'Doğum Günleri',
      description: 'Üyelerinizin doğum günlerini görün ve kutlayın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-birthday-cake',
      priority: 6,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada üyelerinizin doğum günlerini görebilirsiniz. Bugün doğum günü olan üyelerinizi kutlayarak onları mutlu edebilir ve salonunuza olan bağlılıklarını artırabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Bugün doğum günü olan üyeleri görün',
            'Yaklaşan doğum günlerini öğrenin',
            'Üyenin yaşını ve iletişim bilgilerini görün',
            'Doğum günü mesajı gönderin',
            'Özel indirim veya hediye verin',
            'Üye memnuniyetini artırın'
          ]
        },
        {
          type: 'steps',
          content: [
            'Bugün doğum günü olan üyelerin listesini kontrol edin',
            'Üyeyi arayarak doğum gününü kutlayın',
            'Özel bir hediye veya indirim teklif edin',
            'Yaklaşan doğum günleri için hazırlık yapın',
            'Bu özel ilgiyi sosyal medyada paylaşın'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'paymenthistory',
      title: 'Kasa Raporu',
      description: 'Detaylı ödeme geçmişi ve kasa raporlarını görüntüleyin ve analiz edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-cash-register',
      priority: 7,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada salonunuza gelen tüm ödemeleri görebilirsiniz. Hangi üyenin ne zaman, ne kadar ödeme yaptığını, nakit mi kart ile mi ödediğini takip edebilirsiniz. Aylık gelir raporlarınızı da buradan alabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Nakit, kredi kartı ve havale ödemelerini görün',
            'Belirli tarih aralığındaki ödemeleri filtreleyin',
            'Hangi üyenin ne kadar ödediğini öğrenin',
            'Ödeme yöntemlerinin grafiklerini inceleyin',
            'Excel dosyası olarak rapor alın',
            'Günlük, haftalık, aylık gelirleri görün'
          ]
        },
        {
          type: 'steps',
          content: [
            'Görmek istediğiniz tarih aralığını seçin',
            'Belirli bir üyenin ödemelerini aramak için adını yazın',
            'Sadece nakit veya kart ödemelerini görmek için filtre kullanın',
            'Grafikleri inceleyerek gelir trendlerinizi görün',
            'Muhasebe için Excel raporu alın',
            'Düzenli olarak aylık raporlarınızı kontrol edin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'expenses',
      title: 'Gider Yönetimi',
      description: 'Salonunuzun giderlerini kaydedin ve takip edin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-receipt',
      priority: 8,
      content: [
        {
          type: 'text',
          content: 'Bu sayfada salonunuzun tüm giderlerini kaydedebilir ve takip edebilirsiniz. Kira, elektrik, personel maaşları gibi tüm harcamalarınızı kategorilere ayırarak düzenli bir şekilde takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Kira, elektrik, su, doğalgaz giderlerini kaydedin',
            'Personel maaşlarını takip edin',
            'Ekipman alım ve bakım giderlerini girin',
            'Temizlik malzemeleri ve diğer giderleri ekleyin',
            'Gider kategorilerine göre raporlar alın',
            'Aylık toplam giderlerinizi görün'
          ]
        },
        {
          type: 'steps',
          content: [
            'Yeni gider eklemek için "Yeni Gider Ekle" butonuna tıklayın',
            'Gider türünü seçin (kira, elektrik, personel vs.)',
            'Gider tutarını girin',
            'Açıklama ekleyerek detay verin',
            'Gideri kaydedin',
            'Düzenli olarak gider raporlarınızı kontrol edin'
          ]
        }
      ]
    });

    // CRUD İşlemleri Rehberleri
    this.addGuide({
      id: 'membershiptype-add',
      title: 'Üyelik Türü Ekleme',
      description: 'Yeni üyelik türleri oluşturun ve fiyatlandırma yapın',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-plus-circle',
      priority: 9,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuz için yeni üyelik türleri oluşturabilir, branş bazlı fiyatlandırma yapabilir ve üyelik paketlerini özelleştirebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Branş seçimi (Fitness, Boks, MMA, vb.)',
            'Üyelik türü tanımlama',
            'Süre belirleme (gün bazında)',
            'Fiyat belirleme',
            'Özel açıklamalar ekleme',
            'Mevcut türlerin listesi ve yönetimi'
          ]
        },
        {
          type: 'steps',
          content: [
            'Branş seçin (Fitness, Boks, Muay Thai, vb.)',
            'Üyelik türü adını girin (örn: Aylık, 3 Aylık)',
            'Süreyi gün cinsinden belirleyin',
            'Fiyatı Türk Lirası olarak girin',
            'İsteğe bağlı açıklama ekleyin',
            'Kaydet butonuna tıklayarak üyelik türünü oluşturun'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'member-add',
      title: 'Yeni Üye Ekleme',
      description: 'Sisteme yeni üye kaydı oluşturun ve bilgilerini girin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-user-plus',
      priority: 10,
      content: [
        {
          type: 'text',
          content: 'Spor salonunuza yeni üye kaydı oluşturabilir, kişisel bilgilerini girebilir ve üyelik işlemlerini başlatabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Kişisel bilgiler (Ad, soyad)',
            'İletişim bilgileri (Telefon, e-posta)',
            'Adres bilgileri',
            'Doğum tarihi ve cinsiyet seçimi',
            'Form tamamlanma durumu takibi',
            'Zorunlu alan kontrolü'
          ]
        },
        {
          type: 'steps',
          content: [
            'Ad ve soyad bilgilerini girin',
            'Telefon numarası ve e-posta adresini ekleyin',
            'Cinsiyet seçimi yapın (Erkek/Kadın)',
            'İsteğe bağlı doğum tarihi ve adres bilgilerini doldurun',
            'Form ilerlemesini takip edin',
            'Tüm zorunlu alanları doldurduktan sonra kaydedin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'membership-add',
      title: 'Üyelik Süresi Ekleme',
      description: 'Mevcut üyelere yeni üyelik süresi ekleyin ve ödemeleri yönetin',
      category: HelpCategory.CUSTOMER_MANAGEMENT,
      targetRoles: ['owner', 'admin'],
      icon: 'fas fa-id-card',
      priority: 11,
      content: [
        {
          type: 'text',
          content: 'Kayıtlı üyelere yeni üyelik süresi ekleyebilir, ödeme işlemlerini gerçekleştirebilir ve üyelik durumlarını güncelleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Üye seçimi ve arama',
            'Üyelik türü seçimi',
            'Başlangıç tarihi belirleme',
            'Ödeme tutarı ve yöntemi',
            'İndirim ve kampanya uygulamaları',
            'Otomatik süre hesaplama'
          ]
        },
        {
          type: 'steps',
          content: [
            'Üye seçin (üye adı veya telefon numarası ile arama)',
            'Üyelik türünü seçin (branş ve süre)',
            'Başlangıç tarihini belirleyin',
            'Ödeme tutarını kontrol edin',
            'İndirim varsa uygulayın',
            'Ödeme yöntemini seçip işlemi tamamlayın'
          ]
        }
      ]
    });

    // Salon Yönetimi Rehberleri
    this.addGuide({
      id: 'companyuserdetails',
      title: 'Salon Sahipleri',
      description: 'Sistem kullanıcılarını ve salon sahiplerini yönetin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-id-card',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Sisteme kayıtlı salon sahiplerini ve yöneticilerini görüntüleyebilir, bilgilerini düzenleyebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Salon sahipleri listesi',
            'İletişim bilgileri',
            'Salon bağlantıları',
            'Hesap durumları',
            'Son giriş tarihleri'
          ]
        },
        {
          type: 'steps',
          content: [
            'Kullanıcı listesini görüntüleyin',
            'Detay bilgilerini inceleyin',
            'Gerektiğinde bilgileri güncelleyin',
            'Hesap durumlarını kontrol edin'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'license-transactions',
      title: 'Satış Raporları',
      description: 'Lisans satış raporlarını görüntüleyin ve analiz edin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-receipt',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sistem genelindeki lisans satışlarını, gelir analizlerini ve ödeme raporlarını bu panelden takip edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Toplam satış tutarları',
            'Ödeme yöntemlerine göre dağılım',
            'Tarih aralığına göre filtreleme',
            'Admin bazlı satış raporları',
            'Paket bazlı analizler'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'deleted-companies',
      title: 'Pasif Salonlar',
      description: 'Silinen salonları görüntüleyin ve geri yükleyin',
      category: HelpCategory.GYM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-trash-restore',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Sistemden silinen salonları görüntüleyebilir ve gerektiğinde geri yükleyebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Silinen salonlar listesini görüntüleyin',
            'Salon detaylarını inceleyin',
            'Geri yükleme uygunluğunu kontrol edin',
            'Geri yükleme işlemini onaylayın'
          ]
        }
      ]
    });

    // Lisans Yönetimi Rehberleri
    this.addGuide({
      id: 'license-packages-add',
      title: 'Lisans Paketleri',
      description: 'Sistem lisans paketlerini oluşturun ve yönetin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-box-open',
      priority: 2,
      content: [
        {
          type: 'text',
          content: 'Farklı salon ihtiyaçları için lisans paketleri oluşturabilir ve mevcut paketleri yönetebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Paket adını belirleyin',
            'Paket özelliklerini tanımlayın',
            'Fiyatlandırmayı ayarlayın',
            'Süre limitlerini belirleyin',
            'Paketi aktifleştirin'
          ]
        },
        {
          type: 'list',
          content: [
            'Temel paket (küçük salonlar)',
            'Standart paket (orta salonlar)',
            'Premium paket (büyük salonlar)',
            'Kurumsal paket (zincir salonlar)'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'user-licenses',
      title: 'Aktif Lisanslar',
      description: 'Sistemdeki aktif lisansları görüntüleyin ve yönetin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-user-tag',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sistemde aktif olan tüm lisansları görüntüleyebilir, süre uzatabilir veya iptal edebilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif lisans sayısı',
            'Lisans bitiş tarihleri',
            'Paket türleri',
            'Salon bilgileri',
            'Ödeme durumları'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'expired-licenses',
      title: 'Lisansı Dolan Üyeler',
      description: 'Süresi dolan lisansları görüntüleyin ve yenileyin',
      category: HelpCategory.LICENSE_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-exclamation-triangle',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Lisans süresi dolan salonları görüntüleyebilir ve lisans yenileme işlemlerini gerçekleştirebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Süresi dolan lisansları listeleyin',
            'Salon sahipleriyle iletişime geçin',
            'Yenileme teklifini hazırlayın',
            'Ödeme alın ve lisansı yenileyin'
          ]
        }
      ]
    });

    // Sistem Yönetimi - Eksik Paneller
    this.addGuide({
      id: 'devices',
      title: 'Aktif Cihazlar',
      description: 'Sisteme bağlı cihazları görüntüleyin ve yönetin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-mobile-alt',
      priority: 3,
      content: [
        {
          type: 'text',
          content: 'Sisteme bağlı tüm cihazları (mobil, tablet, bilgisayar) görüntüleyebilir ve güvenlik kontrolü yapabilirsiniz.'
        },
        {
          type: 'list',
          content: [
            'Aktif cihaz sayısı',
            'Cihaz türleri ve modelleri',
            'Son bağlantı tarihleri',
            'IP adresleri',
            'Kullanıcı bilgileri'
          ]
        }
      ]
    });

    this.addGuide({
      id: 'cache-admin',
      title: 'Cache Yönetimi',
      description: 'Sistem önbelleğini yönetin ve performansı optimize edin',
      category: HelpCategory.SYSTEM_MANAGEMENT,
      targetRoles: ['owner'],
      icon: 'fas fa-database',
      priority: 4,
      content: [
        {
          type: 'text',
          content: 'Sistem performansını artırmak için önbellek yönetimi yapabilir ve gerektiğinde temizleyebilirsiniz.'
        },
        {
          type: 'steps',
          content: [
            'Cache durumunu kontrol edin',
            'Bellek kullanımını görüntüleyin',
            'Gerektiğinde cache temizleyin',
            'Performans metriklerini inceleyin'
          ]
        }
      ]
    });
  }

  private addGuide(guide: HelpGuide): void {
    this.guides.set(guide.id, guide);
  }

  getGuide(id: string): HelpGuide | undefined {
    return this.guides.get(id);
  }

  getGuidesByCategory(category: HelpCategory): HelpGuide[] {
    return Array.from(this.guides.values())
      .filter(guide => guide.category === category)
      .sort((a, b) => a.priority - b.priority);
  }

  getGuidesByRole(role: string): HelpGuide[] {
    return Array.from(this.guides.values())
      .filter(guide => guide.targetRoles.includes(role))
      .sort((a, b) => a.priority - b.priority);
  }

  openHelpDialog(guideId: string): Observable<any> {
    const guide = this.getGuide(guideId);
    if (!guide) {
      console.warn(`Help guide with id '${guideId}' not found`);
      return new Observable(observer => observer.complete());
    }

    const dialogRef = this.dialog.open(HelpDialogComponent, {
      width: '800px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: { guide, showCloseButton: true } as HelpDialogData,
      disableClose: false,
      position: { top: '30px' },
      panelClass: 'help-dialog-container'
    });

    return dialogRef.afterClosed();
  }

  getAllGuides(): HelpGuide[] {
    return Array.from(this.guides.values())
      .sort((a, b) => a.priority - b.priority);
  }
}
