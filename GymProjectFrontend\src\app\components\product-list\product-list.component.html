<div class="container-fluid mt-4" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Ürünler yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

  <!-- Page Header with Help Button -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex align-items-center gap-2">
            <h5 class="mb-0">
              <i class="fas fa-shopping-basket me-2"></i>
              Ür<PERSON>n <PERSON>
            </h5>

            <!-- Help Button -->
            <app-help-button
              guideId="products"
              position="inline"
              size="small"
              tooltip="Bu panel hakkında yardım al">
            </app-help-button>
          </div>
          <p class="text-muted mb-0 mt-1">
            Ye<PERSON> ürün ekleme ve mevcut ürünleri yönetme
          </p>
        </div>
      </div>
    </div>
  </div>

    <div class="row">
      <!-- Ürün Ekleme Formu -->
      <div class="col-md-4">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <div class="d-flex align-items-center gap-2">
              <h5>Yeni Ürün Ekle</h5>
            </div>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="productForm" (ngSubmit)="addProduct()">
              <div class="modern-form-group">
                <div class="row align-items-center mb-2">
                  <div class="col-3">
                    <label for="name" class="modern-form-label mb-0">Ürün Adı</label>
                  </div>
                  <div class="col-9">
                    <div class="d-flex align-items-center">
                      <div class="input-group-text me-2" style="height: 38px; width: 38px; justify-content: center;">
                        <i class="fas fa-box"></i>
                      </div>
                      <input type="text" class="modern-form-control" id="name" formControlName="name" placeholder="Ürün adı giriniz" style="width: calc(100% - 50px);">
                    </div>
                  </div>
                </div>
              </div>
              <div class="modern-form-group">
                <div class="row align-items-center">
                  <div class="col-3">
                    <label for="price" class="modern-form-label mb-0">Fiyat</label>
                  </div>
                  <div class="col-9">
                    <div class="d-flex align-items-center">
                      <div class="input-group-text me-2" style="height: 38px; width: 38px; justify-content: center;">
                        <i class="fas fa-money-bill-wave"></i>
                      </div>
                      <input type="number" class="modern-form-control" id="price" formControlName="price" placeholder="0.00" style="width: calc(100% - 50px);">
                    </div>
                  </div>
                </div>
              </div>
              
              <button type="submit" class="modern-btn modern-btn-primary w-100" [disabled]="!productForm.valid">
                <i class="fas fa-plus-circle modern-btn-icon"></i> Ürün Ekle
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Ürün Listesi -->
      <div class="col-md-8">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <h5>Ürün Listesi</h5>
            <div class="d-flex align-items-center">
              <div class="position-relative me-2">
                <input type="text" class="modern-form-control" placeholder="Ürün ara..." [(ngModel)]="searchTerm" (keyup.enter)="onSearch()">
                <button class="btn btn-link position-absolute" style="right: 5px; top: 50%; transform: translateY(-50%); border: none; background: none;" (click)="onSearch()">
                  <i class="fas fa-search"></i>
                </button>
              </div>
              <button class="btn btn-outline-secondary btn-sm me-2" (click)="clearSearch()" *ngIf="searchTerm">
                <i class="fas fa-times"></i>
              </button>
              <div class="dropdown">
                <button class="modern-sort-btn dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fas fa-sort me-2"></i>
                  <span class="sort-text">{{getSortText()}}</span>
                  <span class="sort-badge">{{totalItems}}</span>
                </button>
                <ul class="dropdown-menu modern-sort-dropdown" aria-labelledby="sortDropdown">
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'Name' && sortDirection === 'asc'"
                       (click)="sortProducts('Name', 'asc')">
                      <i class="fas fa-sort-alpha-down me-2"></i>
                      <span>İsim (A-Z)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'Name' && sortDirection === 'desc'"
                       (click)="sortProducts('Name', 'desc')">
                      <i class="fas fa-sort-alpha-up me-2"></i>
                      <span>İsim (Z-A)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'Price' && sortDirection === 'asc'"
                       (click)="sortProducts('Price', 'asc')">
                      <i class="fas fa-sort-numeric-down me-2 text-success"></i>
                      <span>Fiyat (Artan)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'Price' && sortDirection === 'desc'"
                       (click)="sortProducts('Price', 'desc')">
                      <i class="fas fa-sort-numeric-up me-2 text-danger"></i>
                      <span>Fiyat (Azalan)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'CreationDate' && sortDirection === 'desc'"
                       (click)="sortProducts('CreationDate', 'desc')">
                      <i class="fas fa-calendar-alt me-2 text-primary"></i>
                      <span>Tarih (Yeni)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'CreationDate' && sortDirection === 'asc'"
                       (click)="sortProducts('CreationDate', 'asc')">
                      <i class="fas fa-calendar-alt me-2 text-secondary"></i>
                      <span>Tarih (Eski)</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Ürün Adı</th>
                    <th>Fiyat</th>
                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let product of products" class="fade-in">
                    <td>{{ product.name }}</td>
                    <td>{{ product.price | currency:'TRY':'symbol-narrow':'1.2-2' }}</td>
                    <td>
                      <button class="modern-btn modern-btn-primary modern-btn-sm me-2" (click)="editProduct(product)">
                        <fa-icon [icon]="faEdit"></fa-icon> Düzenle
                      </button>
                      <button class="modern-btn modern-btn-danger modern-btn-sm" (click)="deleteProduct(product)">
                        <fa-icon [icon]="faTrashAlt"></fa-icon> Sil
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <!-- Sonuç bulunamadı mesajı -->
              <div *ngIf="!isLoading && products.length === 0" class="text-center py-5">
                <div class="text-muted">
                  <i class="fas fa-search fa-3x mb-3"></i>
                  <p>Ürün bulunamadı</p>
                </div>
              </div>

              <!-- Loading spinner -->
              <div *ngIf="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Yükleniyor...</span>
                </div>
              </div>
            </div>
          </div>
          <!-- Pagination Footer -->
          <div class="modern-card-footer" *ngIf="!isLoading && totalItems > 0">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
              <div class="d-flex align-items-center mb-2 mb-md-0">
                <div class="text-muted me-3">
                  Toplam {{ totalItems }} kayıttan {{ (currentPage - 1) * itemsPerPage + 1 }} -
                  {{ currentPage * itemsPerPage > totalItems ? totalItems : currentPage * itemsPerPage }} arası gösteriliyor
                </div>
                <div class="d-flex align-items-center">
                  <label class="form-label me-2 mb-0">Sayfa başına:</label>
                  <select class="form-select form-select-sm" style="width: auto;"
                          [(ngModel)]="itemsPerPage" (change)="changePageSize(itemsPerPage)">
                    <option *ngFor="let size of pageSizeOptions" [value]="size">{{size}}</option>
                  </select>
                </div>
              </div>

              <nav *ngIf="totalPages > 1" aria-label="Page navigation">
                <ul class="modern-pagination">
                  <li class="modern-page-item" [class.disabled]="currentPage === 1">
                    <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)" style="border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);">
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  </li>
                  <li
                    class="modern-page-item"
                    *ngFor="let page of getPaginationRange()"
                    [class.active]="page === currentPage">
                    <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(page)">{{page}}</a>
                  </li>
                  <li class="modern-page-item" [class.disabled]="currentPage === totalPages">
                    <a class="modern-page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)" style="border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;">
                      <i class="fas fa-chevron-right"></i>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
