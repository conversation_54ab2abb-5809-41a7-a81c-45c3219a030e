
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '4aa8a27cc4f1807c4158cb3177ab0729623b0c02c69d7e764853f320602c23a4', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '3406039bfcd94861ba2c4bbeee1d400b761094600fb9995218b4c23ccf20a711', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-R4GLA72F.css': {size: 301949, hash: 'QUb2eeosJCg', text: () => import('./assets-chunks/styles-R4GLA72F_css.mjs').then(m => m.default)}
  },
};
